'use client';

import { useEffect, useRef, useState } from 'react';
import { motion, useScroll, useTransform, useSpring } from 'framer-motion';

interface ScrollTransitionProps {
  children: React.ReactNode;
  nextSection: React.ReactNode;
  className?: string;
}

export default function ScrollTransition({
  children,
  nextSection,
  className = ""
}: ScrollTransitionProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // Portal scroll configuration - triggers only at the very end
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  // Smooth spring animation for the portal transition
  const smoothProgress = useSpring(scrollYProgress, {
    stiffness: 80,
    damping: 25,
    restDelta: 0.001
  });

  // Portal transition - only starts when user tries to scroll past the end (0.9+)
  const scale = useTransform(smoothProgress, [0, 0.9, 0.92, 0.95, 0.98, 1], [1, 1, 1.1, 1.5, 2.5, 4]);
  const opacity = useTransform(smoothProgress, [0, 0.9, 0.93, 0.96, 0.99, 1], [1, 1, 0.95, 0.7, 0.3, 0]);
  const z = useTransform(smoothProgress, [0, 0.9, 0.92, 0.95, 0.98, 1], [0, 0, 30, 150, 400, 800]);

  // Wormhole background effect - dramatic portal opening
  const wormholeOpacity = useTransform(smoothProgress, [0, 0.9, 0.95, 1], [0, 0, 0.7, 1]);
  const wormholeScale = useTransform(smoothProgress, [0, 0.9, 0.95, 1], [0, 0, 1, 2]);

  // Portal container expansion - fills viewport during wormhole
  const portalWidth = useTransform(smoothProgress, [0, 0.9, 0.92], ['100%', '100%', '100vw']);
  const portalHeight = useTransform(smoothProgress, [0, 0.9, 0.92], ['auto', 'auto', '100vh']);
  const portalPosition = useTransform(smoothProgress, [0, 0.9, 0.92], ['relative', 'relative', 'fixed']);

  // Next sections reveal - all sections appear as if contained within the portal
  const nextSectionY = useTransform(smoothProgress, [0, 0.9, 0.95, 0.98, 1], [300, 300, 150, 50, 0]);
  const nextSectionOpacity = useTransform(smoothProgress, [0, 0.9, 0.95, 0.98, 1], [0, 0, 0.4, 0.8, 1]);

  // Portal visual effects - only during wormhole transition
  const blur = useTransform(smoothProgress, [0, 0.9, 0.95, 1], [0, 0, 3, 8]);
  const brightness = useTransform(smoothProgress, [0, 0.9, 0.95, 1], [1, 1, 1.4, 0.5]);



  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    const unsubscribe = smoothProgress.on('change', (latest) => {
      // Portal transition only starts when user reaches the very end (0.9+)
      setIsTransitioning(latest > 0.9);
    });

    return () => unsubscribe();
  }, [smoothProgress]);

  return (
    <div ref={containerRef} className={`relative scroll-transition-container ${className}`}>
      {/* Neural Interface content with portal transition effects */}
      <motion.div
        style={{
          scale,
          opacity,
          z,
          filter: `blur(${blur}px) brightness(${brightness})`,
          width: portalWidth,
          height: portalHeight,
          position: portalPosition,
          top: 0,
          left: 0,
          zIndex: isTransitioning ? 9999 : 10,
        }}
        className={`relative transform-gpu space-transition ${isTransitioning ? 'expanding' : ''}`}
      >
        {children}
      </motion.div>

      {/* Wormhole portal background overlay */}
      <motion.div
        style={{
          opacity: wormholeOpacity,
          scale: wormholeScale,
        }}
        className="fixed inset-0 z-5 pointer-events-none"
      >
        {/* Dimensional portal gradient */}
        <div
          className="absolute inset-0"
          style={{
            background: `radial-gradient(circle at center,
              transparent 0%,
              rgba(102, 102, 255, 0.2) 20%,
              rgba(0, 68, 204, 0.4) 40%,
              rgba(26, 26, 46, 0.8) 70%,
              rgba(0, 0, 0, 0.95) 100%)`
          }}
        />
      </motion.div>

      {/* Dramatic wormhole portal effect during transition */}
      {isMounted && isTransitioning && (
        <div className="fixed inset-0 z-6 pointer-events-none">



        </div>
      )}

      {/* Next section preview */}
      <motion.div
        style={{
          y: nextSectionY,
          opacity: nextSectionOpacity,
        }}
        className="relative z-20"
      >
        {nextSection}
      </motion.div>
    </div>
  );
}
