'use client'

import AboutSection from './components/AboutSection'
import ProjectsSection from './components/ProjectsSection'
import DataVisualization from './components/DataVisualization'
import TestimonialsSection from './components/TestimonialsSection'
import ContactSection from './components/ContactSection'
import StarIcon from './components/StarIcon'
import { useEffect, useState } from 'react'


export default function Home() {
  const [displayText, setDisplayText] = useState('')
  const [currentSkill, setCurrentSkill] = useState(0)
  const fullText = 'Jeetheshwar Aalam • AI/ML Engineer'
  const skills = ['NLP', 'Computer Vision', 'Recommender Systems']

  useEffect(() => {
    let charIndex = 0
    const typingInterval = setInterval(() => {
      setDisplayText(fullText.slice(0, charIndex))
      charIndex++
      if (charIndex > fullText.length) clearInterval(typingInterval)
    }, 100)

    return () => clearInterval(typingInterval)
  }, [])

  useEffect(() => {
    const rotateInterval = setInterval(() => {
      setCurrentSkill(prev => (prev + 1) % skills.length)
    }, 3000)

    return () => clearInterval(rotateInterval)
  }, [])

  return (
    <>
      <main id="home" className="min-h-screen flex flex-col items-center justify-center p-4 relative overflow-hidden">
        {/* Background animated elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-indigo-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>

        <div className="text-center max-w-4xl relative z-10">
          <div className="mb-6">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-4 relative">
              <span className="relative inline-block" style={{ color: '#b3ffff' }}>
                {displayText}
                <span className="ml-2 animate-pulse">|</span>
              </span>
            </h1>
          </div>

          <div className="mb-8">
            <p className="text-xl md:text-2xl lg:text-3xl text-gray-200 relative">
              Specializing in <span className="font-semibold text-white animate-fadeIn">{skills[currentSkill]}</span>
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="star-button">
              <StarIcon className="star-1" />
              <StarIcon className="star-2" />
              <StarIcon className="star-3" />
              <StarIcon className="star-4" />
              <StarIcon className="star-5" />
              <StarIcon className="star-6" />
              View Projects
            </button>
            <button className="bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white font-bold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg border border-white/30 hover:border-white/50">
              Download Résumé
            </button>
          </div>
        </div>

        <div className="absolute bottom-8 animate-bounce">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </main>

      {/* About Section */}
      <AboutSection />

      {/* Projects Section */}
      <ProjectsSection />

      {/* Data Visualization Section */}
      <DataVisualization />

      {/* Testimonials Section */}
      <TestimonialsSection />

      {/* Contact Section */}
      <ContactSection />
    </>
  )
}
