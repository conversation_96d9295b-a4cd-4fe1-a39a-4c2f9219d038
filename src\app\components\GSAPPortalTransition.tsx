'use client';

import { useEffect, useRef, ReactNode } from 'react';

// Dynamic import for GSAP to avoid SSR issues
let gsap: any;
let ScrollTrigger: any;

if (typeof window !== 'undefined') {
  import('gsap').then((gsapModule) => {
    gsap = gsapModule.gsap;
    return import('gsap/ScrollTrigger');
  }).then((scrollTriggerModule) => {
    ScrollTrigger = scrollTriggerModule.ScrollTrigger;
    gsap.registerPlugin(ScrollTrigger);
  });
}

interface GSAPPortalTransitionProps {
  children: ReactNode;
  nextSections: ReactNode;
  className?: string;
}

export default function GSAPPortalTransition({
  children,
  nextSections,
  className = ""
}: GSAPPortalTransitionProps) {
  const portalRef = useRef<HTMLDivElement>(null);
  const nextSectionsRef = useRef<HTMLDivElement>(null);
  const wormholeRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!portalRef.current || !nextSectionsRef.current || !wormholeRef.current) return;

    const portal = portalRef.current;
    const nextSections = nextSectionsRef.current;
    const wormhole = wormholeRef.current;

    // Initialize GSAP animations
    const initGSAP = async () => {
      if (typeof window === 'undefined') return;

      try {
        const gsapModule = await import('gsap');
        const scrollTriggerModule = await import('gsap/ScrollTrigger');

        const gsap = gsapModule.gsap;
        const ScrollTrigger = scrollTriggerModule.ScrollTrigger;

        gsap.registerPlugin(ScrollTrigger);

        // Find the 3D skills container to get precise trigger point
        const skillsContainer = document.getElementById('skills-3d-container') ||
                               portal.querySelector('.gaming-ui-container');
        const triggerElement = skillsContainer || portal;

        console.log('Portal trigger element:', triggerElement);
        console.log('Skills container found:', !!skillsContainer);

        // Create the main timeline with improved timing and accuracy
        const tl = gsap.timeline({
          scrollTrigger: {
            trigger: triggerElement,
            start: "bottom center+=50", // Fine-tuned: Start when 3D skills section bottom hits center+50px
            end: () => "+=" + window.innerHeight * 1.0, // Further reduced to 1.0 viewport height for snappier feel
            pin: true,
            scrub: 0.6, // More responsive scrub value
            anticipatePin: 1,
            invalidateOnRefresh: true, // Recalculate on resize
            markers: false, // Set to true for debugging if needed
            onUpdate: (self) => {
              // Only log at key progress points to reduce console spam
              if (self.progress === 0 || self.progress === 1 || self.progress % 0.25 < 0.01) {
                console.log('Portal scroll progress:', Math.round(self.progress * 100) + '%');
              }
            },
            onStart: () => {
              console.log('🌀 Portal transition started - 3D skills section completed');
            },
            onComplete: () => {
              console.log('✅ Portal transition completed - Entering new dimension');
            }
          }
        });

        // Phase 1: Quick portal expansion (0-25% of scroll) - Faster timing
        tl.fromTo(portal,
          {
            scale: 1,
            borderRadius: "15px",
            rotationX: 0,
            rotationY: 0
          },
          {
            scale: 1.15,
            borderRadius: "8px",
            duration: 0.15, // Reduced from 0.2
            ease: "power2.out"
          }
        )
        // Phase 2: Rapid scaling (25-50% of scroll) - More dramatic and faster
        .to(portal, {
          scale: 1.8,
          borderRadius: "0px",
          duration: 0.15, // Reduced from 0.2
          ease: "power2.inOut"
        })
        .to(portal, {
          scale: 3.5,
          opacity: 0.6,
          duration: 0.15, // Reduced from 0.2
          ease: "power3.in"
        })
        // Phase 3: Final portal dissolution (50-65% of scroll) - Faster completion
        .to(portal, {
          scale: 7,
          opacity: 0.05,
          duration: 0.1, // Kept short for snappy feel
          ease: "power4.in"
        });

        // Phase 2: Enhanced wormhole effects (starts at 15% scroll) - Earlier and faster
        tl.fromTo(wormhole,
          {
            opacity: 0,
            scale: 0.2
          },
          {
            opacity: 0.9,
            scale: 1.8,
            duration: 0.2, // Reduced from 0.3
            ease: "power2.out"
          }, 0.15 // Start earlier
        )
        .to(wormhole, {
          scale: 3.2,
          opacity: 1,
          duration: 0.15, // Reduced from 0.2
          ease: "power2.inOut"
        })
        .to(wormhole, {
          scale: 5.5,
          opacity: 0.2,
          duration: 0.15, // Reduced from 0.2
          ease: "power3.in"
        });

        // Phase 4: Next sections reveal (65-100% of scroll) - Earlier start, smoother transition
        tl.fromTo(nextSections,
          {
            opacity: 0,
            y: window.innerHeight * 1.2, // Reduced from 1.5
            scale: 0.7, // Less dramatic scaling
            rotationX: -15 // Reduced rotation
          },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            rotationX: 0,
            duration: 0.25, // Slightly faster
            ease: "power3.out"
          }, 0.65 // Start earlier at 65% instead of 70%
        );

        // Add refresh on window resize for better responsiveness
        ScrollTrigger.addEventListener("refresh", () => {
          console.log('ScrollTrigger refreshed');
        });

        // Cleanup function
        return () => {
          ScrollTrigger.getAll().forEach(trigger => trigger.kill());
          ScrollTrigger.removeEventListener("refresh");
        };
      } catch (error) {
        console.error('Error initializing GSAP:', error);
      }
    };

    // Add a small delay to ensure DOM is fully rendered
    const timeoutId = setTimeout(() => {
      initGSAP();
    }, 100);

    // Cleanup timeout on unmount
    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  return (
    <div className={`relative ${className}`}>
      {/* Main Portal Section */}
      <div
        ref={portalRef}
        className="portal-section relative min-h-screen overflow-hidden"
        style={{
          background: 'linear-gradient(135deg, rgba(102, 102, 255, 0.15), rgba(0, 0, 0, 0.8))',
          border: '2px solid rgba(102, 102, 255, 0.4)',
          borderRadius: '15px',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 0 30px rgba(102, 102, 255, 0.3), inset 0 0 30px rgba(102, 102, 255, 0.1)'
        }}
      >
        {children}
      </div>

      {/* Wormhole Effect Overlay */}
      <div
        ref={wormholeRef}
        className="fixed inset-0 pointer-events-none z-50"
        style={{
          background: `radial-gradient(circle at center,
            transparent 0%,
            rgba(102, 102, 255, 0.3) 30%,
            rgba(0, 68, 204, 0.6) 60%,
            rgba(0, 0, 0, 0.9) 100%)`,
          opacity: 0
        }}
      >



      </div>

      {/* Next Sections Container */}
      <div
        ref={nextSectionsRef}
        className="next-sections-container relative"
        style={{
          background: 'linear-gradient(180deg, rgba(0, 0, 0, 0.95) 0%, rgba(26, 26, 46, 0.9) 20%, rgba(0, 68, 204, 0.1) 50%, rgba(0, 0, 0, 0.95) 100%)',
          minHeight: '100vh'
        }}
      >
        {nextSections}
      </div>
    </div>
  );
}
