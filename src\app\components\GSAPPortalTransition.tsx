'use client';

import { useEffect, useRef, ReactNode, useState } from 'react';

// Dynamic import for GSAP to avoid SSR issues
let gsap: any;
let ScrollTrigger: any;

if (typeof window !== 'undefined') {
  import('gsap').then((gsapModule) => {
    gsap = gsapModule.gsap;
    return import('gsap/ScrollTrigger');
  }).then((scrollTriggerModule) => {
    ScrollTrigger = scrollTriggerModule.ScrollTrigger;
    gsap.registerPlugin(ScrollTrigger);
  });
}

interface GSAPPortalTransitionProps {
  children: ReactNode;
  nextSections: ReactNode;
  className?: string;
}

export default function GSAPPortalTransition({
  children,
  nextSections,
  className = ""
}: GSAPPortalTransitionProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const portalRef = useRef<HTMLDivElement>(null);
  const nextSectionsRef = useRef<HTMLDivElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    if (!containerRef.current || !portalRef.current || !nextSectionsRef.current || !overlayRef.current) return;

    const container = containerRef.current;
    const portal = portalRef.current;
    const nextSections = nextSectionsRef.current;
    const overlay = overlayRef.current;

    // Initialize GSAP animations
    const initGSAP = async () => {
      if (typeof window === 'undefined') return;

      try {
        const gsapModule = await import('gsap');
        const scrollTriggerModule = await import('gsap/ScrollTrigger');

        const gsap = gsapModule.gsap;
        const ScrollTrigger = scrollTriggerModule.ScrollTrigger;

        gsap.registerPlugin(ScrollTrigger);

        // Enhanced scrollbar management with error handling
        const hideScrollbar = () => {
          try {
            if (document.body) {
              document.body.style.overflow = 'hidden';
              document.body.classList.add('portal-no-scroll');
            }
            if (document.documentElement) {
              document.documentElement.style.overflow = 'hidden';
              document.documentElement.classList.add('portal-transition-active');
            }
          } catch (error) {
            console.warn('Error hiding scrollbar:', error);
          }
        };

        const showScrollbar = () => {
          try {
            if (document.body) {
              document.body.style.overflow = '';
              document.body.classList.remove('portal-no-scroll');
            }
            if (document.documentElement) {
              document.documentElement.style.overflow = '';
              document.documentElement.classList.remove('portal-transition-active');
            }
          } catch (error) {
            console.warn('Error showing scrollbar:', error);
          }
        };

        // Find the 3D skills container for precise trigger point
        const skillsContainer = document.getElementById('skills-3d-container');
        const triggerElement = skillsContainer || portal;

        console.log('🎯 Portal trigger element found:', !!skillsContainer);

        // Create the main portal transition timeline with safety checks
        if (!triggerElement || !container || !portal || !nextSections || !overlay) {
          console.warn('Portal transition elements not found');
          return;
        }

        const portalTimeline = gsap.timeline({
          scrollTrigger: {
            trigger: triggerElement,
            start: "bottom center",
            end: "bottom top",
            pin: container,
            pinSpacing: false,
            scrub: false, // Remove scrub for cleaner transition
            anticipatePin: 1,
            invalidateOnRefresh: true,
            onStart: () => {
              console.log('🌀 Portal transition initiated');
              setIsTransitioning(true);
              hideScrollbar();
            },
            onComplete: () => {
              console.log('✅ Portal transition completed');
              setIsTransitioning(false);
              showScrollbar();
            },
            onRefresh: () => {
              console.log('🔄 Portal transition refreshed');
            }
          }
        });

        // Phase 1: Portal expansion and glow intensification
        portalTimeline
          .to(portal, {
            scale: 1.2,
            borderRadius: "8px",
            boxShadow: "0 0 50px rgba(102, 102, 255, 0.6), inset 0 0 50px rgba(102, 102, 255, 0.2)",
            duration: 0.3,
            ease: "power2.out"
          })
          // Phase 2: Portal opening effect
          .to(portal, {
            scale: 2.5,
            borderRadius: "50%",
            opacity: 0.8,
            duration: 0.4,
            ease: "power2.inOut"
          })
          // Phase 3: Overlay activation
          .fromTo(overlay,
            {
              opacity: 0,
              scale: 0.5
            },
            {
              opacity: 1,
              scale: 1,
              duration: 0.3,
              ease: "power2.out"
            }, "-=0.2")
          // Phase 4: Portal dissolution
          .to(portal, {
            scale: 8,
            opacity: 0,
            duration: 0.5,
            ease: "power3.in"
          })
          // Phase 5: Overlay peak and fade
          .to(overlay, {
            opacity: 0.3,
            duration: 0.3,
            ease: "power2.inOut"
          }, "-=0.3")
          // Phase 6: Next sections reveal
          .fromTo(nextSections,
            {
              opacity: 0,
              y: "100vh",
              scale: 0.8
            },
            {
              opacity: 1,
              y: 0,
              scale: 1,
              duration: 0.6,
              ease: "power3.out"
            }, "-=0.2")
          // Phase 7: Final overlay cleanup
          .to(overlay, {
            opacity: 0,
            duration: 0.4,
            ease: "power2.out"
          }, "-=0.2");

        // Cleanup function
        return () => {
          ScrollTrigger.getAll().forEach(trigger => trigger.kill());
          showScrollbar(); // Ensure scrollbar is restored
        };
      } catch (error) {
        console.error('Error initializing GSAP:', error);
        showScrollbar(); // Restore scrollbar on error
      }
    };

    // Initialize with a small delay to ensure DOM is ready
    const timeoutId = setTimeout(() => {
      initGSAP();
    }, 150);

    // Cleanup on unmount
    return () => {
      clearTimeout(timeoutId);
      if (typeof document !== 'undefined') {
        document.body.style.overflow = '';
        document.documentElement.style.overflow = '';
      }
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={`relative ${className}`}
      style={{
        overflow: isTransitioning ? 'hidden' : 'visible',
        position: 'relative'
      }}
    >
      {/* Main Portal Section - Clean container */}
      <div
        ref={portalRef}
        className="portal-section relative min-h-screen"
        style={{
          background: 'linear-gradient(135deg, rgba(102, 102, 255, 0.15), rgba(0, 0, 0, 0.8))',
          border: '2px solid rgba(102, 102, 255, 0.4)',
          borderRadius: '15px',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 0 30px rgba(102, 102, 255, 0.3), inset 0 0 30px rgba(102, 102, 255, 0.1)',
          overflow: 'hidden',
          position: 'relative',
          zIndex: 1
        }}
      >
        {children}
      </div>

      {/* Portal Transition Overlay - Clean effect */}
      <div
        ref={overlayRef}
        className="fixed inset-0 pointer-events-none"
        style={{
          background: `
            radial-gradient(circle at center,
              transparent 0%,
              transparent 20%,
              rgba(102, 102, 255, 0.4) 40%,
              rgba(0, 68, 204, 0.7) 70%,
              rgba(0, 0, 0, 0.95) 100%
            )
          `,
          opacity: 0,
          scale: 0.5,
          zIndex: 9999,
          mixBlendMode: 'normal'
        }}
      />

      {/* Next Sections Container - Clean background */}
      <div
        ref={nextSectionsRef}
        className="next-sections-container relative"
        style={{
          background: 'linear-gradient(180deg, rgba(0, 0, 0, 0.95) 0%, rgba(26, 26, 46, 0.9) 20%, rgba(0, 68, 204, 0.1) 50%, rgba(0, 0, 0, 0.95) 100%)',
          minHeight: '100vh',
          position: 'relative',
          zIndex: 2,
          overflow: 'hidden'
        }}
      >
        {nextSections}
      </div>
    </div>
  );
}
