'use client';

import { useEffect, useState } from 'react';
import { FaPython, FaRobot, FaBrain, FaChartLine, FaCode, FaDatabase, FaEye, FaNetworkWired } from 'react-icons/fa';

interface SkillData {
  name: string;
  level: number;
  icon: React.ReactNode;
  category: string;
  color: string;
  description: string;
}

interface SkillMeterProps {
  skill: SkillData;
  index: number;
  isVisible: boolean;
}

function SkillMeter({ skill, index, isVisible }: SkillMeterProps) {
  const [animatedLevel, setAnimatedLevel] = useState(0);

  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        setAnimatedLevel(skill.level);
      }, index * 200); // Stagger animations

      return () => clearTimeout(timer);
    }
  }, [isVisible, skill.level, index]);

  return (
    <div className="skill-stat-box group hover:scale-105 transition-all duration-300">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div
            className="p-2 rounded-lg"
            style={{ backgroundColor: `${skill.color}20`, border: `1px solid ${skill.color}` }}
          >
            {skill.icon}
          </div>
          <div>
            <h3 className="text-white font-bold text-lg">{skill.name}</h3>
            <p className="text-gray-400 text-sm">{skill.category}</p>
          </div>
        </div>
        <div className="text-right">
          <span className="text-2xl font-bold" style={{ color: skill.color }}>
            {animatedLevel}%
          </span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="relative h-3 bg-gray-800 rounded-full overflow-hidden">
        <div
          className="absolute top-0 left-0 h-full rounded-full transition-all duration-1000 ease-out"
          style={{
            width: `${animatedLevel}%`,
            background: `linear-gradient(90deg, ${skill.color}, ${skill.color}80)`
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse" />
      </div>

      {/* Skill Description */}
      <p className="text-gray-300 text-sm mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        {skill.description}
      </p>
    </div>
  );
}

interface Interactive3DSkillsProps {
  skills: SkillData[];
  className?: string;
}

export default function Interactive3DSkills({
  skills,
  className = "w-full h-full"
}: Interactive3DSkillsProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Trigger animations when component mounts
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  // Default AI/ML skills if none provided
  const defaultSkills: SkillData[] = [
    {
      name: 'Python',
      level: 95,
      icon: <FaPython className="text-xl" />,
      category: 'Programming',
      color: '#6666ff',
      description: 'Advanced Python programming for AI/ML development and data science'
    },
    {
      name: 'TensorFlow',
      level: 90,
      icon: <FaRobot className="text-xl" />,
      category: 'Deep Learning',
      color: '#0044cc',
      description: 'Building and deploying neural networks with TensorFlow ecosystem'
    },
    {
      name: 'PyTorch',
      level: 88,
      icon: <FaBrain className="text-xl" />,
      category: 'Deep Learning',
      color: '#6666ff',
      description: 'Research and production-ready deep learning models with PyTorch'
    },
    {
      name: 'Scikit-Learn',
      level: 85,
      icon: <FaChartLine className="text-xl" />,
      category: 'Machine Learning',
      color: '#0044cc',
      description: 'Classical machine learning algorithms and model evaluation'
    },
    {
      name: 'Computer Vision',
      level: 82,
      icon: <FaEye className="text-xl" />,
      category: 'AI Specialization',
      color: '#6666ff',
      description: 'Image processing, object detection, and visual recognition systems'
    },
    {
      name: 'NLP',
      level: 80,
      icon: <FaCode className="text-xl" />,
      category: 'AI Specialization',
      color: '#0044cc',
      description: 'Natural Language Processing and text analysis applications'
    },
    {
      name: 'Neural Networks',
      level: 87,
      icon: <FaNetworkWired className="text-xl" />,
      category: 'Deep Learning',
      color: '#6666ff',
      description: 'Designing and optimizing neural network architectures'
    },
    {
      name: 'Data Science',
      level: 83,
      icon: <FaDatabase className="text-xl" />,
      category: 'Analytics',
      color: '#0044cc',
      description: 'Data analysis, visualization, and statistical modeling'
    }
  ];

  const skillsToDisplay = skills.length > 0 ? skills : defaultSkills;

  return (
    <div className={`${className} p-6 overflow-y-auto`} style={{ background: 'transparent' }}>
      {/* Neural Interface Header */}
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold text-white mb-2 glitch-text">
          AI/ML EXPERTISE MATRIX
        </h3>
        <div className="flex justify-center space-x-2 mb-4">
          <div className="sound-wave-indicator"></div>
          <div className="sound-wave-indicator"></div>
          <div className="sound-wave-indicator"></div>
          <div className="sound-wave-indicator"></div>
        </div>
        <p className="text-gray-400 text-sm">
          Neural pathways optimized • Skills matrix active
        </p>
      </div>

      {/* Skills Grid - Responsive */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[400px] md:max-h-[500px] overflow-y-auto pr-2">
        {skillsToDisplay.map((skill, index) => (
          <SkillMeter
            key={skill.name}
            skill={skill}
            index={index}
            isVisible={isVisible}
          />
        ))}
      </div>

      {/* Neural Network Visualization */}
      <div className="mt-8 relative">
        <div className="absolute inset-0 opacity-20">
          <svg width="100%" height="100" viewBox="0 0 400 100" className="overflow-visible">
            {/* Neural network connections */}
            <defs>
              <linearGradient id="neuralGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#6666ff" stopOpacity="0.6" />
                <stop offset="50%" stopColor="#0044cc" stopOpacity="0.8" />
                <stop offset="100%" stopColor="#6666ff" stopOpacity="0.6" />
              </linearGradient>
            </defs>

            {/* Animated neural connections */}
            {[...Array(6)].map((_, i) => (
              <line
                key={i}
                x1={i * 60 + 20}
                y1="20"
                x2={(i + 1) * 60 + 20}
                y2="80"
                stroke="url(#neuralGradient)"
                strokeWidth="2"
                className="animate-pulse"
                style={{ animationDelay: `${i * 0.2}s` }}
              />
            ))}

            {/* Neural nodes */}
            {[...Array(7)].map((_, i) => (
              <circle
                key={i}
                cx={i * 60 + 20}
                cy={i % 2 === 0 ? 20 : 80}
                r="4"
                fill="#6666ff"
                className="animate-pulse"
                style={{ animationDelay: `${i * 0.15}s` }}
              />
            ))}
          </svg>
        </div>

        <div className="text-center py-4">
          <p className="text-gray-500 text-xs">
            Neural Interface • Real-time skill assessment • Matrix synchronized
          </p>
        </div>
      </div>
    </div>
  );
}

// Export for compatibility (no longer needed but keeping for existing imports)
export function preloadInteractive3D(modelPath?: string) {
  console.log('3D model preloading no longer needed - using modern skills display');
}
