'use client';

import { Suspense, useRef, useEffect, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useGLTF, Environment, OrbitControls } from '@react-three/drei';
import * as THREE from 'three';

interface SkillData {
  name: string;
  level: number;
  icon: React.ReactNode;
  position: [number, number, number];
  color: string;
}



interface Interactive3DModelProps {
  modelPath: string;
}

function Interactive3DModel({ modelPath }: Interactive3DModelProps) {
  const modelRef = useRef<THREE.Group>(null);

  // Use useGLTF hook
  const gltf = useGLTF(modelPath);

  useEffect(() => {
    if (gltf && gltf.scene) {
      console.log('✅ GLTF loaded successfully');
    }
  }, [gltf]);

  // Enhanced safety check for GLTF loading
  if (!gltf || !gltf.scene) {
    return (
      <group>
        {/* Fallback 3D object - more interesting than a box */}
        <mesh position={[0, 0, 0]}>
          <icosahedronGeometry args={[2, 1]} />
          <meshStandardMaterial
            color="#6666ff"
            wireframe
            transparent
            opacity={0.8}
          />
        </mesh>


      </group>
    );
  }

  const { scene } = gltf;

  useEffect(() => {
    if (scene) {
      console.log('3D Model loaded and ready');
    }
  }, [scene]);

  useFrame((state) => {
    if (modelRef.current) {
      // Gentle breathing animation - larger base scale
      const breathe = 3.5 + Math.sin(state.clock.elapsedTime * 0.6) * 0.1;
      modelRef.current.scale.setScalar(breathe);

      // Mouse interaction - add safety checks and reduce intensity for stability
      if (state.pointer) {
        modelRef.current.rotation.x = state.pointer.y * 0.1;
        modelRef.current.rotation.z = state.pointer.x * 0.05;
      }
    }
  });



  return (
    <group>
      {/* Main 3D Model - Centered and Prominent */}
      <group ref={modelRef} position={[0, -0.5, 0]} scale={1.5}>
        <primitive object={scene} />
      </group>


    </group>
  );
}

interface Interactive3DSkillsProps {
  modelPath: string;
  skills: SkillData[];
  className?: string;
}

export default function Interactive3DSkills({
  modelPath,
  skills,
  className = "w-full h-full"
}: Interactive3DSkillsProps) {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    // Reset error state when props change
    setHasError(false);
  }, [modelPath, skills]);

  if (hasError) {
    return (
      <div className={`${className} flex items-center justify-center bg-gradient-to-br from-[#6666ff]/10 to-[#0044cc]/10 rounded-lg`}>
        <div className="text-center p-8">
          <div className="w-16 h-16 mx-auto mb-4 border-4 border-[#6666ff] rounded-full flex items-center justify-center">
            <span className="text-[#6666ff] text-2xl">⚠</span>
          </div>
          <p className="text-black font-semibold mb-4">3D Model Loading Error</p>
          <button
            onClick={() => setHasError(false)}
            className="px-6 py-2 bg-[#6666ff] text-black font-semibold rounded-lg hover:bg-[#0044cc] transition-colors"
          >
            Retry Loading
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={className} style={{ background: 'transparent' }}>
      <Canvas
        camera={{ position: [0, 0, 8], fov: 50 }}
        style={{
          background: 'transparent',
          width: '100%',
          height: '100%'
        }}
        dpr={[1, 2]}
        gl={{
          alpha: true,
          antialias: true,
          preserveDrawingBuffer: true
        }}
        onError={(error) => {
          console.error('Canvas error:', error);
          setHasError(true);
        }}
        onCreated={({ gl, scene, camera }) => {
          // Set transparent background
          gl.setClearColor(0x000000, 0);
          scene.background = null;

          // Adjust camera for better model visibility
          camera.position.set(0, 0, 8);
          camera.lookAt(0, 0, 0);

          console.log('✅ Canvas created with transparent background');
          console.log('Camera position:', camera.position);
          console.log('WebGL context:', gl.getContextAttributes());
        }}
      >
        {/* Enhanced Gaming-style Lighting */}
        <ambientLight intensity={0.6} />
        <directionalLight position={[10, 10, 5]} intensity={1.5} color="#6666ff" />
        <directionalLight position={[-10, -10, -5]} intensity={1.0} color="#0044cc" />
        <pointLight position={[0, 0, 10]} intensity={1.2} color="#ffffff" />
        <spotLight
          position={[0, 10, 0]}
          angle={0.3}
          penumbra={1}
          intensity={0.8}
          color="#6666ff"
        />

        {/* Environment */}
        <Environment preset="night" environmentIntensity={0.4} />

        {/* Interactive Controls */}
        <OrbitControls
          enablePan={false}
          enableZoom={false}
          enableRotate={true}
          autoRotate={false}
          rotateSpeed={0.5}
        />

        {/* Interactive 3D Model with Skills */}
        <Suspense fallback={
          <group>
            {/* Enhanced fallback with neural network appearance */}
            <mesh position={[0, 0, 0]}>
              <icosahedronGeometry args={[2, 1]} />
              <meshStandardMaterial
                color="#6666ff"
                wireframe
                transparent
                opacity={0.8}
              />
            </mesh>



            {/* Pulsing core */}
            <mesh position={[0, 0, 0]}>
              <sphereGeometry args={[0.5, 16, 16]} />
              <meshStandardMaterial
                color="#6666ff"
                emissive="#6666ff"
                emissiveIntensity={0.5}
              />
            </mesh>


          </group>
        }>
          <Interactive3DModel modelPath={modelPath} />
        </Suspense>
      </Canvas>
    </div>
  );
}

// Preload function
export function preloadInteractive3D(modelPath: string) {
  useGLTF.preload(modelPath);
}
