'use client';

import { useEffect, useState } from 'react';
import { FaPython, FaRobot, FaBrain, FaChartLine, FaCode, FaDatabase, FaEye, FaNetworkWired } from 'react-icons/fa';

interface SkillData {
  name: string;
  level: number;
  icon: React.ReactNode;
  category: string;
  color: string;
  description: string;
}

interface SkillMeterProps {
  skill: SkillData;
  index: number;
  isVisible: boolean;
}

function SkillMeter({ skill, index, isVisible }: SkillMeterProps) {
  const [animatedLevel, setAnimatedLevel] = useState(0);

  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        setAnimatedLevel(skill.level);
      }, index * 200); // Stagger animations

      return () => clearTimeout(timer);
    }
  }, [isVisible, skill.level, index]);

  return (
    <div className="glass rounded-xl p-6 group hover:scale-105 transition-all duration-300 shadow-lg">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="p-3 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20">
            {skill.icon}
          </div>
          <div>
            <h3 className="text-white font-bold text-lg">{skill.name}</h3>
            <p className="text-gray-300 text-sm">{skill.category}</p>
          </div>
        </div>
        <div className="text-right">
          <span className="text-2xl font-bold text-purple-400">
            {animatedLevel}%
          </span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="relative h-2 bg-white/10 rounded-full overflow-hidden mb-3">
        <div
          className="absolute top-0 left-0 h-full rounded-full transition-all duration-1000 ease-out bg-gradient-to-r from-purple-500 to-indigo-500"
          style={{ width: `${animatedLevel}%` }}
        />
      </div>

      {/* Skill Description */}
      <p className="text-gray-300 text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        {skill.description}
      </p>
    </div>
  );
}

interface Interactive3DSkillsProps {
  skills: SkillData[];
  className?: string;
}

export default function Interactive3DSkills({
  skills,
  className = "w-full h-full"
}: Interactive3DSkillsProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Trigger animations when component mounts
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  // Default AI/ML skills if none provided
  const defaultSkills: SkillData[] = [
    {
      name: 'Python',
      level: 95,
      icon: <FaPython className="text-xl text-blue-400" />,
      category: 'Programming',
      color: '#6366f1',
      description: 'Advanced Python programming for AI/ML development and data science'
    },
    {
      name: 'TensorFlow',
      level: 90,
      icon: <FaRobot className="text-xl text-orange-400" />,
      category: 'Deep Learning',
      color: '#8b5cf6',
      description: 'Building and deploying neural networks with TensorFlow ecosystem'
    },
    {
      name: 'PyTorch',
      level: 88,
      icon: <FaBrain className="text-xl text-red-400" />,
      category: 'Deep Learning',
      color: '#6366f1',
      description: 'Research and production-ready deep learning models with PyTorch'
    },
    {
      name: 'Scikit-Learn',
      level: 85,
      icon: <FaChartLine className="text-xl text-green-400" />,
      category: 'Machine Learning',
      color: '#8b5cf6',
      description: 'Classical machine learning algorithms and model evaluation'
    },
    {
      name: 'Computer Vision',
      level: 82,
      icon: <FaEye className="text-xl text-purple-400" />,
      category: 'AI Specialization',
      color: '#6366f1',
      description: 'Image processing, object detection, and visual recognition systems'
    },
    {
      name: 'NLP',
      level: 80,
      icon: <FaCode className="text-xl text-indigo-400" />,
      category: 'AI Specialization',
      color: '#8b5cf6',
      description: 'Natural Language Processing and text analysis applications'
    },
    {
      name: 'Neural Networks',
      level: 87,
      icon: <FaNetworkWired className="text-xl text-cyan-400" />,
      category: 'Deep Learning',
      color: '#6366f1',
      description: 'Designing and optimizing neural network architectures'
    },
    {
      name: 'Data Science',
      level: 83,
      icon: <FaDatabase className="text-xl text-yellow-400" />,
      category: 'Analytics',
      color: '#8b5cf6',
      description: 'Data analysis, visualization, and statistical modeling'
    }
  ];

  const skillsToDisplay = skills.length > 0 ? skills : defaultSkills;

  return (
    <div className={`${className}`}>
      {/* Clean Professional Header */}
      <div className="text-center mb-12">
        <h3 className="text-2xl font-bold text-white mb-4">
          Technical Expertise
        </h3>
        <p className="text-gray-300 max-w-2xl mx-auto">
          Specialized skills in artificial intelligence, machine learning, and data science with hands-on experience in modern frameworks and technologies.
        </p>
      </div>

      {/* Skills Grid - Clean and Professional */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {skillsToDisplay.map((skill, index) => (
          <SkillMeter
            key={skill.name}
            skill={skill}
            index={index}
            isVisible={isVisible}
          />
        ))}
      </div>
    </div>
  );
}

// Export for compatibility (no longer needed but keeping for existing imports)
export function preloadInteractive3D(modelPath?: string) {
  console.log('3D model preloading no longer needed - using modern skills display');
}
