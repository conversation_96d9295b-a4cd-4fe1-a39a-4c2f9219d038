'use client';

import { useState, ChangeEvent, FormEvent } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaEnvelope, FaLinkedin, FaGithub, FaTwitter, FaRocket, FaCode, FaDownload, FaExternalLinkAlt } from 'react-icons/fa';
import StarIcon from './StarIcon';

interface FormData {
  name: string;
  email: string;
  message: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  message?: string;
}

export default function ContactSection() {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    message: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const validate = (): FormErrors => {
    const newErrors: FormErrors = {};
    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }
    if (!formData.message.trim()) newErrors.message = 'Message is required';
    return newErrors;
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const validationErrors = validate();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSuccess(true);
      setFormData({ name: '', email: '', message: '' });

      // Reset success message after 5 seconds
      setTimeout(() => setIsSuccess(false), 5000);
    }, 1500);
  };

  return (
    <section id="contact" className="relative py-20">
      <div className="container mx-auto px-4">
        {/* Gaming-style Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white font-mono tracking-wider drop-shadow-lg mb-4" style={{
            fontFamily: 'Courier New, monospace',
            textShadow: '0 0 10px #6666ff, 0 0 20px #6666ff, 0 0 30px #6666ff'
          }}>
            NEURAL LINK ESTABLISHED
          </h2>
          <div className="flex justify-center space-x-2 mb-4">
            <div className="sound-wave-indicator"></div>
            <div className="sound-wave-indicator"></div>
            <div className="sound-wave-indicator"></div>
            <div className="sound-wave-indicator"></div>
          </div>
          <p className="text-lg text-white font-semibold font-mono tracking-wide">
            Initiating communication protocols • Ready to connect
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Contact Form - Gaming UI */}
          <div className="lg:col-span-2">
            <div className="gaming-ui-container p-8">
              {isSuccess ? (
                <div className="text-center py-12">
                  <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center" style={{
                    background: 'linear-gradient(135deg, #6666ff, #0044cc)',
                    boxShadow: '0 0 30px rgba(102, 102, 255, 0.5)'
                  }}>
                    <FaCheck className="text-3xl text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-2 glitch-text">
                    TRANSMISSION SUCCESSFUL
                  </h3>
                  <p className="text-gray-300">
                    Neural link established • Response incoming • Stand by...
                  </p>
                </div>
              ) : (
                <>
                  <h3 className="text-xl font-bold text-white mb-6 glitch-text">
                    INITIATE COMMUNICATION PROTOCOL
                  </h3>

                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="skill-stat-box">
                      <label className="block text-white text-sm font-bold mb-2">
                        IDENTITY MATRIX
                      </label>
                      <div className="relative">
                        <input
                          type="text"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          className={`w-full px-4 py-3 bg-black/50 text-white rounded-lg border-2 transition-all duration-300 ${
                            errors.name
                              ? 'border-red-500 focus:border-red-400'
                              : 'border-[#6666ff]/50 focus:border-[#6666ff] focus:shadow-[0_0_10px_rgba(102,102,255,0.5)]'
                          } focus:outline-none backdrop-blur-sm`}
                          placeholder="Enter your designation..."
                        />
                        {errors.name && (
                          <p className="text-red-400 text-sm mt-1 font-mono">{errors.name}</p>
                        )}
                      </div>
                    </div>

                    <div className="skill-stat-box">
                      <label className="block text-white text-sm font-bold mb-2">
                        COMMUNICATION CHANNEL
                      </label>
                      <div className="relative">
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          className={`w-full px-4 py-3 bg-black/50 text-white rounded-lg border-2 transition-all duration-300 ${
                            errors.email
                              ? 'border-red-500 focus:border-red-400'
                              : 'border-[#0044cc]/50 focus:border-[#0044cc] focus:shadow-[0_0_10px_rgba(0,68,204,0.5)]'
                          } focus:outline-none backdrop-blur-sm`}
                          placeholder="<EMAIL>"
                        />
                        {errors.email && (
                          <p className="text-red-400 text-sm mt-1 font-mono">{errors.email}</p>
                        )}
                      </div>
                    </div>

                    <div className="skill-stat-box">
                      <label className="block text-white text-sm font-bold mb-2">
                        DATA TRANSMISSION
                      </label>
                      <div className="relative">
                        <textarea
                          name="message"
                          value={formData.message}
                          onChange={handleChange}
                          rows={5}
                          className={`w-full px-4 py-3 bg-black/50 text-white rounded-lg border-2 transition-all duration-300 resize-none ${
                            errors.message
                              ? 'border-red-500 focus:border-red-400'
                              : 'border-[#6666ff]/50 focus:border-[#6666ff] focus:shadow-[0_0_10px_rgba(102,102,255,0.5)]'
                          } focus:outline-none backdrop-blur-sm`}
                          placeholder="Encode your message here..."
                        ></textarea>
                        {errors.message && (
                          <p className="text-red-400 text-sm mt-1 font-mono">{errors.message}</p>
                        )}
                      </div>
                    </div>

                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full star-button flex items-center justify-center text-lg font-bold"
                    >
                      <StarIcon className="star-1" />
                      <StarIcon className="star-2" />
                      <StarIcon className="star-3" />
                      <StarIcon className="star-4" />
                      <StarIcon className="star-5" />
                      <StarIcon className="star-6" />
                      {isSubmitting ? (
                        <>
                          <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          TRANSMITTING...
                        </>
                      ) : (
                        <>
                          <FaRocket className="mr-2" />
                          LAUNCH TRANSMISSION
                        </>
                      )}
                    </button>
                  </form>
                </>
              )}
            </div>
          </div>

          {/* Neural Network Connections */}
          <div className="lg:col-span-1 space-y-6">
            {/* Connection Nodes */}
            <div className="gaming-ui-container p-6">
              <h3 className="text-lg font-bold text-white mb-6 glitch-text">
                NEURAL NETWORK NODES
              </h3>

              <div className="space-y-4">
                <div className="skill-stat-box group cursor-pointer hover:scale-105 transition-all duration-300">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{
                      background: 'linear-gradient(135deg, #6666ff, #0044cc)',
                      boxShadow: '0 0 20px rgba(102, 102, 255, 0.3)'
                    }}>
                      <FaEnvelope className="text-xl text-white" />
                    </div>
                    <div>
                      <h4 className="font-bold text-white">DIRECT LINK</h4>
                      <p className="text-gray-300 text-sm font-mono"><EMAIL></p>
                    </div>
                  </div>
                </div>

                <div className="skill-stat-box group cursor-pointer hover:scale-105 transition-all duration-300">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{
                      background: 'linear-gradient(135deg, #0044cc, #6666ff)',
                      boxShadow: '0 0 20px rgba(0, 68, 204, 0.3)'
                    }}>
                      <FaLinkedin className="text-xl text-white" />
                    </div>
                    <div>
                      <h4 className="font-bold text-white">PROFESSIONAL NET</h4>
                      <p className="text-gray-300 text-sm font-mono">linkedin.com/in/jeetheshwar</p>
                    </div>
                  </div>
                </div>

                <div className="skill-stat-box group cursor-pointer hover:scale-105 transition-all duration-300">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{
                      background: 'linear-gradient(135deg, #6666ff, #0044cc)',
                      boxShadow: '0 0 20px rgba(102, 102, 255, 0.3)'
                    }}>
                      <FaGithub className="text-xl text-white" />
                    </div>
                    <div>
                      <h4 className="font-bold text-white">CODE REPOSITORY</h4>
                      <p className="text-gray-300 text-sm font-mono">github.com/Jeetheshwar</p>
                    </div>
                  </div>
                </div>

                <div className="skill-stat-box group cursor-pointer hover:scale-105 transition-all duration-300">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{
                      background: 'linear-gradient(135deg, #0044cc, #6666ff)',
                      boxShadow: '0 0 20px rgba(0, 68, 204, 0.3)'
                    }}>
                      <FaDownload className="text-xl text-white" />
                    </div>
                    <div>
                      <h4 className="font-bold text-white">RESUME DOWNLOAD</h4>
                      <p className="text-gray-300 text-sm font-mono">neural.profile.pdf</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Neural Feed Subscription */}
            <div className="gaming-ui-container p-6">
              <h3 className="text-lg font-bold text-white mb-4 glitch-text">
                NEURAL FEED SYNC
              </h3>
              <p className="text-gray-300 mb-6 text-sm">
                Subscribe to receive AI insights, project updates, and neural network discoveries directly to your feed.
              </p>

              <form className="space-y-4">
                <div className="skill-stat-box">
                  <input
                    type="email"
                    placeholder="<EMAIL>"
                    className="w-full px-4 py-3 bg-black/50 text-white rounded-lg border-2 border-[#6666ff]/50 focus:border-[#6666ff] focus:shadow-[0_0_10px_rgba(102,102,255,0.5)] focus:outline-none backdrop-blur-sm transition-all duration-300"
                  />
                </div>
                <button
                  type="submit"
                  className="w-full star-button flex items-center justify-center"
                >
                  <StarIcon className="star-1" />
                  <StarIcon className="star-2" />
                  <StarIcon className="star-3" />
                  <StarIcon className="star-4" />
                  <StarIcon className="star-5" />
                  <StarIcon className="star-6" />
                  SYNC NEURAL FEED
                </button>
              </form>
            </div>

            {/* Status Indicators */}
            <div className="gaming-ui-container p-6">
              <h3 className="text-lg font-bold text-white mb-4 glitch-text">
                SYSTEM STATUS
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">Neural Interface</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-green-400 text-xs font-mono">ONLINE</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">Communication Link</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-green-400 text-xs font-mono">ACTIVE</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">Response Time</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                    <span className="text-blue-400 text-xs font-mono">&lt; 24H</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}