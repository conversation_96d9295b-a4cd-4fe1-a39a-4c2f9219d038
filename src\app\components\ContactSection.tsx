'use client';

import { useState, ChangeEvent, FormEvent } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaEnvelope, FaLinkedin, FaGithub, FaTwitter, FaRocket, FaCode, FaDownload, FaExternalLinkAlt } from 'react-icons/fa';
import { SiGmail, SiLinkedin, SiGithub, SiTwitter } from 'react-icons/si';
import StarIcon from './StarIcon';

interface FormData {
  name: string;
  email: string;
  message: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  message?: string;
}

export default function ContactSection() {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    message: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const validate = (): FormErrors => {
    const newErrors: FormErrors = {};
    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }
    if (!formData.message.trim()) newErrors.message = 'Message is required';
    return newErrors;
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const validationErrors = validate();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSuccess(true);
      setFormData({ name: '', email: '', message: '' });

      // Reset success message after 5 seconds
      setTimeout(() => setIsSuccess(false), 5000);
    }, 1500);
  };

  return (
    <section id="contact" className="relative py-20">
      <div className="container mx-auto px-4">
        {/* Clean Professional Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4" style={{ color: '#b3ffff' }}>
            Get In Touch
          </h2>
          <p className="text-lg text-gray-200 max-w-2xl mx-auto">
            Let's connect and discuss opportunities, collaborations, or any questions you might have about AI/ML projects.
          </p>
        </div>

        <div className="flex flex-col gap-12">
          {/* Contact Form - Clean Professional */}
          <div className="max-w-4xl mx-auto w-full">
            <div className="glass rounded-2xl shadow-xl p-8">
              {isSuccess ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-6">
                    <FaCheck className="text-3xl text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="text-2xl font-bold text-oxford-blue dark:text-white mb-2">
                    Message Sent!
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Thanks for reaching out. I'll get back to you soon.
                  </p>
                </div>
              ) : (
                <>
                  <h3 className="text-xl font-bold text-oxford-blue dark:text-white mb-6">
                    Send me a message
                  </h3>

                  <form onSubmit={handleSubmit}>
                    <div className="mb-6">
                      <div className="relative">
                        <input
                          type="text"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          className={`w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-lg border ${
                            errors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                          } focus:outline-none focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 text-gray-900 dark:text-white`}
                          placeholder="Your Name"
                        />
                        {errors.name && (
                          <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                        )}
                      </div>
                    </div>

                    <div className="mb-6">
                      <div className="relative">
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          className={`w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-lg border ${
                            errors.email ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                          } focus:outline-none focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 text-gray-900 dark:text-white`}
                          placeholder="Your Email"
                        />
                        {errors.email && (
                          <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                        )}
                      </div>
                    </div>

                    <div className="mb-6">
                      <div className="relative">
                        <textarea
                          name="message"
                          value={formData.message}
                          onChange={handleChange}
                          rows={5}
                          className={`w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-lg border ${
                            errors.message ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                          } focus:outline-none focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 text-gray-900 dark:text-white resize-none`}
                          placeholder="Your Message"
                        ></textarea>
                        {errors.message && (
                          <p className="text-red-500 text-sm mt-1">{errors.message}</p>
                        )}
                      </div>
                    </div>

                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full relative group"
                    >
                      <div className="relative inline-block p-px font-semibold leading-6 text-white bg-gray-800 shadow-2xl cursor-pointer rounded-xl shadow-zinc-900 transition-transform duration-300 ease-in-out hover:scale-105 active:scale-95 w-full">
                        <span className="absolute inset-0 rounded-xl bg-gradient-to-r from-teal-400 via-blue-500 to-purple-500 p-[2px] opacity-0 transition-opacity duration-500 group-hover:opacity-100"></span>

                        <span className="relative z-10 block px-6 py-3 rounded-xl bg-gray-950 w-full">
                          <div className="relative z-10 flex items-center justify-center space-x-2">
                            <span className="transition-all duration-500 group-hover:translate-x-1">
                              {isSubmitting ? 'Sending...' : 'Send Message'}
                            </span>
                            {isSubmitting ? (
                              <svg className="animate-spin h-5 w-5 transition-transform duration-500 group-hover:translate-x-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                            ) : (
                              <svg
                                className="w-5 h-5 transition-transform duration-500 group-hover:translate-x-1"
                                aria-hidden="true"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  clipRule="evenodd"
                                  d="M8.22 5.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L11.94 10 8.22 6.28a.75.75 0 0 1 0-1.06Z"
                                  fillRule="evenodd"
                                ></path>
                              </svg>
                            )}
                          </div>
                        </span>
                      </div>
                    </button>
                  </form>
                </>
              )}
            </div>
          </div>

          {/* Newsletter Signup */}
          <div className="max-w-2xl mx-auto w-full">
            <div className="glass rounded-2xl shadow-xl p-8">
              <h3 className="text-xl font-bold text-oxford-blue dark:text-white mb-4">
                Stay Updated
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Subscribe to my newsletter for AI insights and project updates.
              </p>

              <form className="space-y-4">
                <div>
                  <input
                    type="email"
                    placeholder="Your email address"
                    className="w-full px-4 py-3 glass rounded-lg border border-white/20 focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  />
                </div>
                <button
                  type="submit"
                  className="w-full relative group"
                >
                  <div className="relative inline-block p-px font-semibold leading-6 text-white bg-gray-800 shadow-2xl cursor-pointer rounded-xl shadow-zinc-900 transition-transform duration-300 ease-in-out hover:scale-105 active:scale-95 w-full">
                    <span className="absolute inset-0 rounded-xl bg-gradient-to-r from-teal-400 via-blue-500 to-purple-500 p-[2px] opacity-0 transition-opacity duration-500 group-hover:opacity-100"></span>

                    <span className="relative z-10 block px-6 py-3 rounded-xl bg-gray-950 w-full">
                      <div className="relative z-10 flex items-center justify-center space-x-2">
                        <span className="transition-all duration-500 group-hover:translate-x-1">
                          Subscribe
                        </span>
                        <svg
                          className="w-5 h-5 transition-transform duration-500 group-hover:translate-x-1"
                          aria-hidden="true"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clipRule="evenodd"
                            d="M8.22 5.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L11.94 10 8.22 6.28a.75.75 0 0 1 0-1.06Z"
                            fillRule="evenodd"
                          ></path>
                        </svg>
                      </div>
                    </span>
                  </div>
                </button>
              </form>
            </div>
          </div>

          {/* Contact Information - Moved to Bottom */}
          <div className="max-w-4xl mx-auto w-full">
            <div className="glass rounded-2xl shadow-xl p-8">
              <h3 className="text-xl font-bold text-oxford-blue dark:text-white mb-8 text-center">
                Let's Connect
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <a
                  href="mailto:<EMAIL>"
                  className="flex flex-col items-center p-6 rounded-xl bg-white/5 hover:bg-white/10 transition-all duration-300 hover:scale-105 group"
                >
                  <div className="w-16 h-16 flex items-center justify-center mb-4">
                    <SiGmail className="text-4xl text-red-500 group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <h4 className="font-semibold text-white mb-2">Email</h4>
                  <p className="text-gray-300 text-sm text-center"><EMAIL></p>
                </a>

                <a
                  href="https://linkedin.com/in/jeetheshwar"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex flex-col items-center p-6 rounded-xl bg-white/5 hover:bg-white/10 transition-all duration-300 hover:scale-105 group"
                >
                  <div className="w-16 h-16 flex items-center justify-center mb-4">
                    <SiLinkedin className="text-4xl text-blue-600 group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <h4 className="font-semibold text-white mb-2">LinkedIn</h4>
                  <p className="text-gray-300 text-sm text-center">linkedin.com/in/jeetheshwar</p>
                </a>

                <a
                  href="https://github.com/Jeetheshwar"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex flex-col items-center p-6 rounded-xl bg-white/5 hover:bg-white/10 transition-all duration-300 hover:scale-105 group"
                >
                  <div className="w-16 h-16 flex items-center justify-center mb-4">
                    <SiGithub className="text-4xl text-gray-100 group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <h4 className="font-semibold text-white mb-2">GitHub</h4>
                  <p className="text-gray-300 text-sm text-center">github.com/Jeetheshwar</p>
                </a>

                <a
                  href="https://twitter.com/jeetheshwar_ai"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex flex-col items-center p-6 rounded-xl bg-white/5 hover:bg-white/10 transition-all duration-300 hover:scale-105 group"
                >
                  <div className="w-16 h-16 flex items-center justify-center mb-4">
                    <SiTwitter className="text-4xl text-sky-400 group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <h4 className="font-semibold text-white mb-2">Twitter</h4>
                  <p className="text-gray-300 text-sm text-center">@jeetheshwar_ai</p>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}